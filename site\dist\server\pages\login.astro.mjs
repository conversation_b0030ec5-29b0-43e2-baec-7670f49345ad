import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { B as Button, a as authClient, $ as $$MainLayout } from '../chunks/MainLayout_CUaM-0Uq.mjs';
import { jsxs, jsx } from 'react/jsx-runtime';
import { useState } from 'react';
import { I as Input } from '../chunks/input_D169YdkW.mjs';
export { renderers } from '../renderers.mjs';

function LoginForm() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const onSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);
    try {
      await authClient.signIn.email(
        { email, password },
        {
          onError: (ctx) => setError(ctx.error.message ?? "Ошибка входа"),
          onSuccess: () => {
            window.location.href = "/account";
          }
        }
      );
    } finally {
      setLoading(false);
    }
  };
  return /* @__PURE__ */ jsxs("form", { className: "space-y-4", onSubmit, children: [
    /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsx("label", { className: "text-sm", children: "Email" }),
      /* @__PURE__ */ jsx(Input, { type: "email", value: email, onChange: (e) => setEmail(e.target.value), required: true })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsx("label", { className: "text-sm", children: "Пароль" }),
      /* @__PURE__ */ jsx(Input, { type: "password", value: password, onChange: (e) => setPassword(e.target.value), required: true })
    ] }),
    error && /* @__PURE__ */ jsx("div", { className: "text-sm text-red-500", children: error }),
    /* @__PURE__ */ jsx(Button, { type: "submit", disabled: loading, children: loading ? "Входим..." : "Войти" })
  ] });
}

const $$Astro = createAstro();
const $$Login = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Login;
  if (Astro2.locals.user) {
    return Astro2.redirect("/account");
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u0412\u0445\u043E\u0434" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<section class="container py-10"> <div class="mx-auto max-w-md"> <h1 class="mb-6 text-2xl font-bold">Вход</h1> ${renderComponent($$result2, "LoginForm", LoginForm, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/auth/LoginForm", "client:component-export": "default" })} <p class="mt-4 text-sm text-muted-foreground">
Нет аккаунта? <a href="/register" class="underline">Зарегистрируйтесь</a> </p> </div> </section> ` })}`;
}, "D:/Dev/parttec/site/src/pages/login.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/login.astro";
const $$url = "/login";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Login,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
