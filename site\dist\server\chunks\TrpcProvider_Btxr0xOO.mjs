import { jsx } from 'react/jsx-runtime';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { loggerLink, httpBatchLink } from '@trpc/client';
import { useState } from 'react';
import { a as trpc } from './badge_QUEVfRm2.mjs';

const baseURL = "http://localhost:3000";
function TrpcProvider({ children }) {
  const [queryClient] = useState(
    () => new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: 5 * 6e4,
          // 5 минут - данные каталога меняются редко
          gcTime: 30 * 6e4,
          // 30 минут в кэше
          retry: 2,
          refetchOnWindowFocus: false,
          refetchOnReconnect: true,
          // Оптимизация для каталога
          refetchOnMount: false,
          refetchInterval: false
        },
        mutations: {
          retry: 1
        }
      }
    })
  );
  const [trpcClient] = useState(
    () => trpc.createClient({
      links: [
        loggerLink({
          enabled: () => false
        }),
        httpBatchLink({
          url: `${baseURL}/trpc`,
          fetch: (url, options) => fetch(url, {
            ...options,
            credentials: "include"
          })
        })
      ]
    })
  );
  return /* @__PURE__ */ jsx(trpc.Provider, { client: trpcClient, queryClient, children: /* @__PURE__ */ jsx(QueryClientProvider, { client: queryClient, children }) });
}

export { TrpcProvider as T };
