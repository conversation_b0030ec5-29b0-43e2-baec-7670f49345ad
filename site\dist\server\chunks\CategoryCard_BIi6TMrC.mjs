import { jsxs, jsx } from 'react/jsx-runtime';
import { C as Card, b as <PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON><PERSON>, d as CardDescription, a as CardContent } from './card_BqdjJz59.mjs';
import { B as Badge } from './badge_QUEVfRm2.mjs';
import { n as navigate } from './router_vN4ZPF0m.mjs';

function CategoryCard({ category, onClick }) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      navigate(`/catalog/categories/${category.slug}`);
    }
  };
  return /* @__PURE__ */ jsxs(
    Card,
    {
      className: "cursor-pointer transition-all hover:shadow-md hover:scale-[1.02]",
      onClick: handleClick,
      children: [
        /* @__PURE__ */ jsx(CardHeader, { className: "pb-3", children: /* @__PURE__ */ jsxs("div", { className: "flex items-start justify-between", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
            /* @__PURE__ */ jsxs(CardTitle, { className: "text-lg line-clamp-2 flex items-center gap-2", children: [
              category.icon && /* @__PURE__ */ jsx("span", { className: "text-xl", children: category.icon }),
              category.name
            ] }),
            category.description && /* @__PURE__ */ jsx(CardDescription, { className: "mt-1 line-clamp-2", children: category.description })
          ] }),
          category.image && /* @__PURE__ */ jsx(
            "img",
            {
              src: category.image.url,
              alt: category.name,
              className: "w-16 h-16 object-cover rounded-md ml-3"
            }
          )
        ] }) }),
        /* @__PURE__ */ jsxs(CardContent, { className: "pt-0", children: [
          /* @__PURE__ */ jsx("div", { className: "flex items-center justify-between", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
            /* @__PURE__ */ jsxs(Badge, { variant: "secondary", children: [
              "Уровень ",
              category.level
            ] }),
            category._count && /* @__PURE__ */ jsxs(Badge, { variant: "outline", children: [
              category._count.parts,
              " запчастей"
            ] })
          ] }) }),
          category.children && category.children.length > 0 && /* @__PURE__ */ jsxs("div", { className: "mt-3 pt-3 border-t", children: [
            /* @__PURE__ */ jsx("div", { className: "text-xs text-muted-foreground mb-2", children: "Подкатегории:" }),
            /* @__PURE__ */ jsxs("div", { className: "flex flex-wrap gap-1", children: [
              category.children.slice(0, 3).map((child) => /* @__PURE__ */ jsx(Badge, { variant: "outline", className: "text-xs", children: child.name }, child.id)),
              category.children.length > 3 && /* @__PURE__ */ jsxs(Badge, { variant: "outline", className: "text-xs", children: [
                "+",
                category.children.length - 3
              ] })
            ] })
          ] })
        ] })
      ]
    }
  );
}

export { CategoryCard as C };
