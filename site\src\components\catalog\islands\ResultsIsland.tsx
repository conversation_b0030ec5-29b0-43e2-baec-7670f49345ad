"use client"

import { useState, useEffect } from "react"
import {
  BarChart3,
  Grid3X3,
  List,
  Eye,
  FileText,
  ArrowLeftRight as Compare,
  Package,
  TrendingUp,
  Clock,
  Building,
  ImageIcon,
} from "lucide-react"
import { ModernButton } from "@/components/ui/modern-button"
import { ModernCard, ModernCardContent } from "@/components/ui/modern-card"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { StatusBadge } from "@/components/ui/status-badge"
import { MediaThumbnail } from "../pro/MediaThumbnail"
import { AISearchSuggestions } from "../pro/AISearchSuggestions"
import { useCatalogSearch } from "../pro/useCatalogSearch"
import type { PartApplicability } from "@/types/catalog"

import { TrpcBoundary } from "@/components/providers/TrpcBoundary"

export default function ResultsIsland() {
  return (
    <TrpcBoundary>
      <ResultsIslandInner />
    </TrpcBoundary>
  )
}

function ResultsIslandInner() {
  const { results, filters, updateFilters, filteredCount, clearFilters } = useCatalogSearch()
  const [viewMode, setViewMode] = useState<"detailed" | "grid" | "table">("detailed")
  const [selectedItems, setSelectedItems] = useState<number[]>([])

  // Обработчик для открытия деталей элемента
  const handleViewDetails = (applicability: PartApplicability) => {
    const event = new CustomEvent('openItemDetails', { detail: applicability })
    window.dispatchEvent(event)
  }

  const handleAIApplyFilters = (aiFilters: Partial<typeof filters>) => {
    updateFilters(aiFilters)
  }

  const handleAIUpdateQuery = (query: string) => {
    updateFilters({ query })
  }

  const handleOpenAI = () => {
    const event = new CustomEvent('openAIAssistant')
    window.dispatchEvent(event)
  }

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="container max-w-none p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold tracking-tight">Результаты поиска</h2>

          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 p-1 bg-muted/50 rounded border border-border/40">
              <ModernButton 
                variant={viewMode === "detailed" ? "default" : "ghost"} 
                size="sm" 
                onClick={() => setViewMode("detailed")} 
                className="h-7 w-7 p-0"
              >
                <List className="h-3 w-3" />
              </ModernButton>
              <ModernButton 
                variant={viewMode === "grid" ? "default" : "ghost"} 
                size="sm" 
                onClick={() => setViewMode("grid")} 
                className="h-7 w-7 p-0"
              >
                <Grid3X3 className="h-3 w-3" />
              </ModernButton>
              <ModernButton 
                variant={viewMode === "table" ? "default" : "ghost"} 
                size="sm" 
                onClick={() => setViewMode("table")} 
                className="h-7 w-7 p-0"
              >
                <BarChart3 className="h-3 w-3" />
              </ModernButton>
            </div>

            <ModernButton variant="outline" size="sm" className="gap-1 h-7 px-2 text-xs">
              <Compare className="h-3 w-3" />
              Сравнить
              {selectedItems.length > 0 && (
                <Badge variant="secondary" className="ml-1 h-4 w-4 rounded-full p-0 text-xs">
                  {selectedItems.length}
                </Badge>
              )}
            </ModernButton>
          </div>
        </div>

        <AISearchSuggestions 
          query={filters.query} 
          resultsCount={filteredCount} 
          onApplyFilters={handleAIApplyFilters} 
          onUpdateQuery={handleAIUpdateQuery} 
        />

        {results.length === 0 ? (
          <ModernCard variant="elevated" className="text-center py-12 border-2 border-dashed border-border-strong">
            <ModernCardContent>
              <div className="flex flex-col items-center gap-3">
                <div className="h-12 w-12 rounded-full bg-muted/50 flex items-center justify-center">
                  <Package className="h-6 w-6 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-1">Ничего не найдено</h3>
                  <p className="text-muted-foreground text-sm max-w-md">
                    Попробуйте изменить критерии поиска или воспользуйтесь AI ассистентом для помощи
                  </p>
                </div>
                <div className="flex gap-2">
                  <ModernButton variant="outline" onClick={clearFilters} size="sm">
                    Очистить фильтры
                  </ModernButton>
                  <ModernButton variant="gradient" onClick={handleOpenAI} size="sm">
                    Спросить AI
                  </ModernButton>
                </div>
              </div>
            </ModernCardContent>
          </ModernCard>
        ) : (
          <div className="space-y-3 animate-fade-in">
            {results.map((applicability, index) => (
              <ModernCard 
                key={applicability.id} 
                variant="elevated" 
                className="group hover:shadow-strong transition-all duration-200 animate-slide-up border hover:border-primary/20" 
                style={{ animationDelay: `${index * 30}ms` }}
              >
                <ModernCardContent className="p-4">
                  <div className="flex gap-4">
                    <div className="flex-shrink-0">
                      <MediaThumbnail 
                        mediaAsset={applicability.catalogItem.image ?? undefined} 
                        size="md" 
                        className="cursor-pointer hover:scale-105 transition-transform" 
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-3 mb-2">
                            <Checkbox
                              checked={selectedItems.includes(applicability.id)}
                              onCheckedChange={(checked) => {
                                if (checked) setSelectedItems([...selectedItems, applicability.id])
                                else setSelectedItems(selectedItems.filter((id) => id !== applicability.id))
                              }}
                              className="data-[state=checked]:bg-primary data-[state=checked]:border-primary h-4 w-4"
                            />
                            <div className="min-w-0 flex-1">
                              <h3 className="text-lg font-bold text-primary font-mono tracking-tight truncate">
                                {applicability.catalogItem.sku}
                              </h3>
                              <div className="flex items-center gap-2 mt-1">
                                <Badge variant="outline" className="font-medium text-xs h-5">
                                  <Building className="h-3 w-3 mr-1" />
                                  {applicability.catalogItem.brand.name}
                                </Badge>
                                {applicability.catalogItem.brand.isOem && (
                                  <Badge variant="secondary" className="text-xs h-5">OEM</Badge>
                                )}
                                {applicability.catalogItem.mediaAssets.length > 0 && (
                                  <Badge variant="outline" className="text-xs h-5">
                                    <ImageIcon className="h-3 w-3 mr-1" />
                                    {applicability.catalogItem.mediaAssets.length}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>

                          <div className="space-y-1 mb-3">
                            <h4 className="font-semibold text-sm">{applicability.part.name}</h4>
                            <p className="text-muted-foreground text-sm leading-relaxed line-clamp-2">
                              {applicability.catalogItem.description}
                            </p>
                          </div>

                          {applicability.notes && (
                            <div className="p-2 rounded bg-info/10 border border-info/20 mb-3">
                              <div className="flex items-start gap-2">
                                <div className="h-4 w-4 rounded-full bg-info/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                                  <div className="h-1.5 w-1.5 rounded-full bg-info" />
                                </div>
                                <p className="text-xs text-info-foreground leading-relaxed">{applicability.notes}</p>
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="flex flex-col items-end gap-2 ml-4">
                          <StatusBadge status={applicability.accuracy as any} size="sm" />
                          <Badge variant="outline" className="text-xs">
                            {applicability.part.partCategory.name}
                          </Badge>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-1 mb-3">
                        {applicability.catalogItem.attributes.slice(0, 6).map((attr) => (
                          <div 
                            key={attr.id} 
                            className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-card border border-border-strong hover:border-primary/40 transition-colors text-xs"
                          >
                            <span 
                              className="text-muted-foreground font-medium truncate max-w-[80px]" 
                              title={attr.template.title}
                            >
                              {attr.template.title.split(" ")[0]}:
                            </span>
                            <span className="font-mono font-semibold">{attr.value}</span>
                            {attr.template.unit && (
                              <span className="text-muted-foreground">{attr.template.unit}</span>
                            )}
                          </div>
                        ))}
                        {applicability.catalogItem.attributes.length > 6 && (
                          <div className="inline-flex items-center px-2 py-1 rounded-full bg-muted/50 border border-border text-xs text-muted-foreground">
                            +{applicability.catalogItem.attributes.length - 6} еще
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between pt-2 border-t border-border">
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            <span>{new Date(applicability.part.updatedAt).toLocaleDateString("ru-RU")}</span>
                          </div>
                          <Separator orientation="vertical" className="h-3" />
                          <div className="flex items-center gap-1">
                            <Badge variant="outline" className="text-xs font-mono h-4 px-1">
                              #{applicability.partId}
                            </Badge>
                            <Badge variant="outline" className="text-xs font-mono h-4 px-1">
                              #{applicability.catalogItemId}
                            </Badge>
                          </div>
                        </div>

                        <div className="flex items-center gap-1">
                          <ModernButton 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleViewDetails(applicability)} 
                            className="gap-1 h-7 px-2 text-xs"
                          >
                            <Eye className="h-3 w-3" />Подробности
                          </ModernButton>
                          <ModernButton variant="outline" size="sm" className="gap-1 h-7 px-2 text-xs">
                            <FileText className="h-3 w-3" />Техданные
                          </ModernButton>
                          <ModernButton variant="gradient" size="sm" className="gap-1 h-7 px-2 text-xs">
                            <TrendingUp className="h-3 w-3" />В корзину
                          </ModernButton>
                        </div>
                      </div>
                    </div>
                  </div>
                </ModernCardContent>
              </ModernCard>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
