import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead, h as addAttribute } from '../../../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../../chunks/MainLayout_CUaM-0Uq.mjs';
import { t as trpcClient, B as Badge } from '../../../chunks/badge_QUEVfRm2.mjs';
import { T as TrpcProvider } from '../../../chunks/TrpcProvider_Btxr0xOO.mjs';
import { C as Card, b as CardHeader, c as CardTitle, a as CardContent, d as CardDescription } from '../../../chunks/card_BqdjJz59.mjs';
export { renderers } from '../../../renderers.mjs';

const $$Astro = createAstro();
const $$id = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$id;
  const { id } = Astro2.params;
  if (!id || isNaN(Number(id))) {
    return Astro2.redirect("/catalog");
  }
  let part = null;
  try {
    part = await trpcClient.crud.part.findUnique.query({
      where: { id: Number(id) },
      include: {
        image: true,
        partCategory: { include: { image: true } },
        parent: { include: { partCategory: true } },
        children: { include: { partCategory: true } },
        attributes: {
          include: {
            template: {
              include: {
                group: true,
                synonymGroups: { include: { synonyms: true } }
              }
            }
          }
        },
        applicabilities: {
          include: {
            catalogItem: {
              include: {
                brand: true,
                attributes: {
                  include: {
                    template: { include: { group: true } }
                  }
                }
              }
            }
          }
        },
        equipmentApplicabilities: {
          include: {
            equipmentModel: {
              include: {
                brand: true,
                attributes: {
                  include: {
                    template: { include: { group: true } }
                  }
                }
              }
            }
          }
        }
      }
    });
  } catch (error) {
    console.error("Error loading part details:", error);
  }
  if (!part) {
    return Astro2.redirect("/catalog");
  }
  const attributeGroups = part.attributes.reduce((groups, attr) => {
    const groupName = attr.template.group?.name || "\u041E\u0441\u043D\u043E\u0432\u043D\u044B\u0435 \u0445\u0430\u0440\u0430\u043A\u0442\u0435\u0440\u0438\u0441\u0442\u0438\u043A\u0438";
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(attr);
    return groups;
  }, {});
  const getAccuracyVariant = (accuracy) => {
    switch (accuracy) {
      case "EXACT_MATCH":
        return "success";
      case "MATCH_WITH_NOTES":
        return "warning";
      case "REQUIRES_MODIFICATION":
        return "secondary";
      case "PARTIAL_MATCH":
        return "outline";
      default:
        return "default";
    }
  };
  const getAccuracyText = (accuracy) => {
    switch (accuracy) {
      case "EXACT_MATCH":
        return "\u0422\u043E\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435";
      case "MATCH_WITH_NOTES":
        return "\u0421\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435 \u0441 \u043F\u0440\u0438\u043C\u0435\u0447\u0430\u043D\u0438\u044F\u043C\u0438";
      case "REQUIRES_MODIFICATION":
        return "\u0422\u0440\u0435\u0431\u0443\u0435\u0442 \u0434\u043E\u0440\u0430\u0431\u043E\u0442\u043A\u0438";
      case "PARTIAL_MATCH":
        return "\u0427\u0430\u0441\u0442\u0438\u0447\u043D\u043E\u0435 \u0441\u043E\u0432\u043F\u0430\u0434\u0435\u043D\u0438\u0435";
      default:
        return accuracy;
    }
  };
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${part.id}`, "description": `\u0414\u0435\u0442\u0430\u043B\u044C\u043D\u0430\u044F \u0438\u043D\u0444\u043E\u0440\u043C\u0430\u0446\u0438\u044F \u043E \u0433\u0440\u0443\u043F\u043F\u0435 \u0432\u0437\u0430\u0438\u043C\u043E\u0437\u0430\u043C\u0435\u043D\u044F\u0435\u043C\u043E\u0441\u0442\u0438 ${part.name || part.id}` }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <!-- Хлебные крошки --> <nav class="flex mb-8" aria-label="Breadcrumb"> <ol class="inline-flex items-center space-x-1 md:space-x-3"> <li class="inline-flex items-center"> <a href="/" class="inline-flex items-center text-sm font-medium text-muted-foreground hover:text-foreground">
Главная
</a> </li> <li> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <a href="/catalog" class="text-sm font-medium text-muted-foreground hover:text-foreground">Каталог</a> </div> </li> <li> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <a${addAttribute(`/catalog/categories/${part.partCategory.slug}`, "href")} class="text-sm font-medium text-muted-foreground hover:text-foreground"> ${part.partCategory.name} </a> </div> </li> <li aria-current="page"> <div class="flex items-center"> <span class="mx-2 text-muted-foreground">/</span> <span class="text-sm font-medium text-foreground">${part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${part.id}`}</span> </div> </li> </ol> </nav> <div class="grid grid-cols-1 lg:grid-cols-3 gap-8"> <!-- Основная информация --> <div class="lg:col-span-2 space-y-6"> <!-- Заголовок и основная информация --> <div> <h1 class="text-3xl font-bold tracking-tight mb-2"> ${part.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${part.id}`} </h1> <p class="text-muted-foreground mb-4">
Категория: ${part.partCategory.name} </p>  ${part.parent && renderTemplate`<div class="mb-4"> <p class="text-sm text-muted-foreground">
Входит в:
<a${addAttribute(`/catalog/parts/${part.parent.id}`, "href")} class="text-primary hover:underline ml-1"> ${part.parent.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${part.parent.id}`} </a> </p> </div>`} </div> <!-- Атрибуты по группам --> ${Object.keys(attributeGroups).length > 0 && renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardHeader", CardHeader, {}, { "default": async ($$result5) => renderTemplate` ${renderComponent($$result5, "CardTitle", CardTitle, {}, { "default": async ($$result6) => renderTemplate`Характеристики` })} ` })} ${renderComponent($$result4, "CardContent", CardContent, {}, { "default": async ($$result5) => renderTemplate` <div class="space-y-6"> ${Object.entries(attributeGroups).map(([groupName, attrs]) => renderTemplate`<div> <h4 class="font-semibold mb-3">${groupName}</h4> <div class="grid grid-cols-1 md:grid-cols-2 gap-3"> ${attrs.map((attr) => renderTemplate`<div class="flex justify-between items-center py-2 border-b border-border/50"> <span class="text-sm text-muted-foreground">${attr.template.title}:</span> <span class="font-medium"> ${attr.value} ${attr.template.unit && ` ${attr.template.unit}`} </span> </div>`)} </div> </div>`)} </div> ` })} ` })}`} <!-- Дочерние элементы --> ${part.children.length > 0 && renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardHeader", CardHeader, {}, { "default": async ($$result5) => renderTemplate` ${renderComponent($$result5, "CardTitle", CardTitle, {}, { "default": async ($$result6) => renderTemplate`Входящие компоненты` })} ${renderComponent($$result5, "CardDescription", CardDescription, {}, { "default": async ($$result6) => renderTemplate`Детали, входящие в состав этого узла` })} ` })} ${renderComponent($$result4, "CardContent", CardContent, {}, { "default": async ($$result5) => renderTemplate` <div class="space-y-2"> ${part.children.map((child) => renderTemplate`<a${addAttribute(`/catalog/parts/${child.id}`, "href")} class="block p-3 rounded-md border hover:bg-muted/50 transition-colors"> <div class="font-medium">${child.name || `\u0417\u0430\u043F\u0447\u0430\u0441\u0442\u044C #${child.id}`}</div> <div class="text-sm text-muted-foreground">${child.partCategory.name}</div> </a>`)} </div> ` })} ` })}`} </div> <!-- Боковая панель --> <div class="space-y-6"> <!-- Изображение --> ${part.image && renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardContent", CardContent, { "class": "p-4" }, { "default": async ($$result5) => renderTemplate` <img${addAttribute(part.image.url, "src")}${addAttribute(part.name || "\u0418\u0437\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0438", "alt")} class="w-full h-auto rounded-md"> ` })} ` })}`} <!-- Взаимозаменяемые артикулы --> ${part.applicabilities.length > 0 && renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardHeader", CardHeader, {}, { "default": async ($$result5) => renderTemplate` ${renderComponent($$result5, "CardTitle", CardTitle, {}, { "default": async ($$result6) => renderTemplate`Взаимозаменяемые артикулы` })} ${renderComponent($$result5, "CardDescription", CardDescription, {}, { "default": async ($$result6) => renderTemplate`Конкретные запчасти от производителей` })} ` })} ${renderComponent($$result4, "CardContent", CardContent, {}, { "default": async ($$result5) => renderTemplate` <div class="space-y-3"> ${part.applicabilities.map((applicability) => renderTemplate`<div class="p-3 rounded-md border"> <div class="flex items-start justify-between mb-2"> <div> <div class="font-medium">${applicability.catalogItem.sku}</div> <div class="text-sm text-muted-foreground">${applicability.catalogItem.brand.name}</div> </div> ${renderComponent($$result5, "Badge", Badge, { "variant": getAccuracyVariant(applicability.accuracy) }, { "default": async ($$result6) => renderTemplate`${getAccuracyText(applicability.accuracy)}` })} </div> ${applicability.notes && renderTemplate`<p class="text-xs text-muted-foreground mt-2">${applicability.notes}</p>`} ${applicability.catalogItem.description && renderTemplate`<p class="text-xs text-muted-foreground mt-1">${applicability.catalogItem.description}</p>`} </div>`)} </div> ` })} ` })}`} <!-- Применимость к технике --> ${part.equipmentApplicabilities.length > 0 && renderTemplate`${renderComponent($$result3, "Card", Card, {}, { "default": async ($$result4) => renderTemplate` ${renderComponent($$result4, "CardHeader", CardHeader, {}, { "default": async ($$result5) => renderTemplate` ${renderComponent($$result5, "CardTitle", CardTitle, {}, { "default": async ($$result6) => renderTemplate`Применимость к технике` })} ${renderComponent($$result5, "CardDescription", CardDescription, {}, { "default": async ($$result6) => renderTemplate`Модели техники, где используется эта запчасть` })} ` })} ${renderComponent($$result4, "CardContent", CardContent, {}, { "default": async ($$result5) => renderTemplate` <div class="space-y-3"> ${part.equipmentApplicabilities.map((applicability) => renderTemplate`<div class="p-3 rounded-md border"> <div class="font-medium">${applicability.equipmentModel.name}</div> ${applicability.equipmentModel.brand && renderTemplate`<div class="text-sm text-muted-foreground">${applicability.equipmentModel.brand.name}</div>`} ${applicability.notes && renderTemplate`<p class="text-xs text-muted-foreground mt-2">${applicability.notes}</p>`} </div>`)} </div> ` })} ` })}`} </div> </div> </div> ` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/catalog/parts/[id].astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/catalog/parts/[id].astro";
const $$url = "/catalog/parts/[id]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$id,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
