"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { motion, useInView } from "framer-motion"
import { useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { GridPattern } from "@/components/ui/grid-pattern"
import Ripple from "@/components/ui/ripple"
import { ManufacturersNetwork } from "@/components/manufacturers-network"
import {
  Search,
  Database,
  CheckCircle,
  ArrowRight,
  Settings,
  Star,
  Clock,
  Users,
  Shield,
  Zap,
  Target,
  Building2,
  Factory,
  Cpu,
  ChevronRight,
  Info,
  AlertTriangle,
  CheckCircle2,
  Sparkles,
  Globe,
  TrendingUp,
  Mic,
  MessageSquare,
  Brain,
  Headphones,
  Bot,
  Wand2,
} from "lucide-react"

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] },
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

function AnimatedSection({ children, className = "" }: { children: React.ReactNode; className?: string }) {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 60 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 60 }}
      transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

function AISearchDemo() {
  const [isListening, setIsListening] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [aiResponse, setAiResponse] = useState("")
  const [step, setStep] = useState(0)

  const demoSteps = [
    {
      query: "Нужен сальник для гидроцилиндра Caterpillar 320D",
      response:
        "Анализирую запрос... Найдено 15 совместимых сальников для гидроцилиндра Caterpillar 320D. Показываю варианты с экономией до 45%.",
    },
    {
      query: "Масляный фильтр для двигателя Cummins ISX15",
      response: "Обрабатываю... Найдено 8 аналогов масляного фильтра. Все варианты в наличии, совместимость 98-100%.",
    },
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      const currentStep = demoSteps[step]
      setSearchQuery(currentStep.query)
      setTimeout(() => {
        setAiResponse(currentStep.response)
      }, 1500)
      setTimeout(() => {
        setStep((prev) => (prev + 1) % demoSteps.length)
        setSearchQuery("")
        setAiResponse("")
      }, 4000)
    }, 6000)

    return () => clearInterval(interval)
  }, [step])

  return (
    <div className="relative">
      <div className="absolute inset-0 bg-zinc-900/5 rounded-2xl" />
      <Ripple />

      <div className="relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-800 rounded-2xl p-6 shadow-2xl">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* AI Search Interface */}
          <div className="space-y-3">
            <div className="flex items-center gap-3 mb-3">
              <div className="relative">
                <div className="w-12 h-12 bg-zinc-800 rounded-xl flex items-center justify-center">
                  <Brain className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse" />
              </div>
              <div>
                <h3 className="text-2xl font-semibold text-white">ИИ-Ассистент поиска запчастей</h3>
                <p className="text-base text-gray-100">Умный поиск с голосовым вводом</p>
              </div>
            </div>

            <div className="relative">
              <div className="absolute -inset-0.5 bg-blue-500 rounded-lg blur opacity-30" />
              <div className="relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-800 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-4">
                  <Search className="w-5 h-5 text-gray-300" />
                  <input
                    type="text"
                    placeholder="Опишите нужную запчасть или технику..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 bg-transparent text-white placeholder-gray-300 focus:outline-none"
                  />
                  <motion.button
                    onClick={() => setIsListening(!isListening)}
                    className={`p-2 rounded-lg transition-all ${
                      isListening
                        ? "bg-red-500 text-white animate-pulse"
                        : "bg-zinc-800 text-gray-300 hover:bg-zinc-700"
                    }`}
                  >
                    <Mic className="w-4 h-4" />
                  </motion.button>
                </div>

                {aiResponse && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-zinc-800 border border-zinc-700 rounded-lg p-4"
                  >
                    <div className="flex items-start gap-3">
                      <Bot className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                      <div className="text-base text-gray-100">{aiResponse}</div>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <MessageSquare className="w-4 h-4 text-blue-400" />
                  <span className="text-base font-medium text-white">Текстовый ввод</span>
                </div>
                <p className="text-sm text-gray-100">Опишите деталь естественным языком</p>
              </div>
              <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Headphones className="w-4 h-4 text-blue-400" />
                  <span className="text-base font-medium text-white">Голосовой ввод</span>
                </div>
                <p className="text-sm text-gray-100">Говорите - ИИ поймет и найдет</p>
              </div>
            </div>
          </div>

          {/* AI Features */}
          <div className="space-y-3">
            <h4 className="text-2xl font-semibold text-white flex items-center gap-2">
              <Wand2 className="w-5 h-5 text-blue-400" />
              Возможности ИИ-поиска
            </h4>

            <div className="space-y-4">
              {[
                {
                  icon: Brain,
                  title: "Понимание контекста",
                  description: "ИИ анализирует техническое описание и находит точные аналоги",
                  color: "bg-zinc-700",
                },
                {
                  icon: MessageSquare,
                  title: "Диалоговый режим",
                  description: "Задавайте уточняющие вопросы для точного поиска",
                  color: "bg-zinc-700",
                },
                {
                  icon: Target,
                  title: "Умная фильтрация",
                  description: "Автоматический подбор по техническим характеристикам",
                  color: "bg-zinc-700",
                },
                {
                  icon: Zap,
                  title: "Мгновенный результат",
                  description: "Поиск за секунды вместо часов навигации по каталогу",
                  color: "bg-zinc-700",
                },
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start gap-4 p-4 bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg  transition-all duration-300"
                >
                  <div
                    className={`w-10 h-10 ${feature.color} rounded-lg flex items-center justify-center flex-shrink-0`}
                  >
                    <feature.icon className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h5 className="font-medium text-white mb-1">{feature.title}</h5>
                    <p className="text-base text-gray-100">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function TechnicalSchema() {
  const [activeConnection, setActiveConnection] = useState<number | null>(null)
  const [searchQuery, setSearchQuery] = useState("")

  const parts = [
    {
      id: 1,
      original: "Corteco 12345-ABC",
      specs: "25×47×7mm, NBR, -40°C/+120°C",
      alternatives: ["SKF 789-XYZ", "Febi 456-DEF", "NOK 321-GHI"],
      category: "Сальники",
      compatibility: 98,
      savings: 35,
    },
    {
      id: 2,
      original: "John Deere RE12345",
      specs: "Передаточное число 1:4.5, крутящий момент 850 Нм",
      alternatives: ["Komatsu 708-1W-00151", "Caterpillar 123-4567"],
      category: "Редукторы",
      compatibility: 95,
      savings: 45,
    },
  ]

  return (
    <div className="relative">
      <div className="absolute inset-0 bg-zinc-900/5 rounded-2xl" />

      <div className="relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-800 rounded-2xl p-6 shadow-2xl">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Search Interface */}
          <div className="space-y-3">
            <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg">
              <div className="p-6 border-b border-zinc-700">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-zinc-700 rounded-lg flex items-center justify-center">
                    <Search className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-2xl font-semibold text-white">Поиск взаимозаменяемых деталей</h3>
                </div>

                <div className="relative group">
                  <div className="absolute -inset-0.5 bg-blue-500 rounded-lg blur opacity-30 group-hover:opacity-60 transition duration-300" />
                  <div className="relative">
                    <Search className="absolute left-4 top-4 w-5 h-5 text-gray-300" />
                    <input
                      type="text"
                      placeholder="Введите артикул или OEM номер..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-12 pr-4 py-4 bg-zinc-900/50 backdrop-blur-xl border border-zinc-700 rounded-lg text-white placeholder-gray-300 focus:border-blue-500/50 focus:outline-none focus:ring-2 focus:ring-blue-500/20 transition-all duration-300"
                    />
                  </div>
                </div>
              </div>

              <div className="p-6 space-y-3">
                {parts.map((part) => (
                  <motion.div
                    key={part.id}
                    className={`p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                      activeConnection === part.id
                        ? "bg-blue-500/20 border border-blue-500/50"
                        : "bg-zinc-800 backdrop-blur-xl border border-zinc-700 hover:border-zinc-600"
                    }`}
                    onClick={() => setActiveConnection(activeConnection === part.id ? null : part.id)}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-mono text-base text-white font-medium">{part.original}</span>
                      <Badge className="bg-zinc-700 text-white text-sm border-0">{part.category}</Badge>
                    </div>
                    <div className="text-sm text-gray-100 mb-3">{part.specs}</div>
                    <div className="flex items-center gap-4 text-sm">
                      <span className="text-green-400 flex items-center gap-1">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                        Совместимость: {part.compatibility}%
                      </span>
                      <span className="text-blue-400 flex items-center gap-1">
                        <TrendingUp className="w-3 h-3" />
                        Экономия: до {part.savings}%
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Results Panel */}
          <div className="space-y-3">
            <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-lg">
              <div className="p-6 border-b border-zinc-700">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 bg-zinc-700 rounded-lg flex items-center justify-center">
                    <Database className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-2xl font-semibold text-white">Результаты анализа</h3>
                </div>
              </div>

              <div className="p-6">
                {activeConnection ? (
                  <div className="space-y-4">
                    {(() => {
                      const part = parts.find((p) => p.id === activeConnection)
                      if (!part) return null

                      return (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="space-y-4"
                        >
                          <div className="bg-zinc-800 backdrop-blur-xl rounded-xl p-4 border border-zinc-700">
                            <div className="text-base font-medium text-white mb-2 flex items-center gap-2">
                              <Target className="w-4 h-4 text-blue-400" />
                              Оригинальная деталь:
                            </div>
                            <div className="font-mono text-blue-400 text-2xl">{part.original}</div>
                            <div className="text-sm text-gray-100 mt-2">{part.specs}</div>
                          </div>

                          <div className="space-y-3">
                            <div className="text-base font-medium text-white flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                              Найденные аналоги:
                            </div>
                            {part.alternatives.map((alt, index) => (
                              <motion.div
                                key={index}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="flex items-center gap-3 p-3 bg-zinc-800 rounded-xl border border-zinc-700 backdrop-blur-xl"
                              >
                                <CheckCircle2 className="w-4 h-4 text-green-400 flex-shrink-0" />
                                <div className="flex-1">
                                  <div className="font-mono text-base text-white">{alt}</div>
                                  <div className="text-sm text-gray-100">Физическая совместимость подтверждена</div>
                                </div>
                                <div className="text-sm text-green-400 font-medium">-{part.savings}%</div>
                              </motion.div>
                            ))}
                          </div>

                          <div className="bg-zinc-800 border border-zinc-700 rounded-xl p-4 backdrop-blur-xl">
                            <div className="flex items-center gap-2 text-blue-400 text-base font-medium mb-2">
                              <Info className="w-4 h-4" />
                              Техническое заключение
                            </div>
                            <div className="text-sm text-gray-100">
                              Все найденные аналоги имеют идентичные технические характеристики и могут использоваться
                              как прямая замена без модификаций.
                            </div>
                          </div>
                        </motion.div>
                      )
                    })()}
                  </div>
                ) : (
                  <div className="py-12 text-center">
                    <motion.div className="w-16 h-16 bg-zinc-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                      <Database className="w-8 h-8 text-white" />
                    </motion.div>
                    <div className="text-gray-300">Выберите деталь для анализа совместимости</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

const catalogSectionsData = [
  {
    id: "seals",
    name: "Сальники и уплотнения",
    icon: Settings,
    active: true,
    description: "Комплексная база данных сальников, манжет и уплотнительных элементов",
    detailedInfo: {
      totalParts: "52,847",
      manufacturers: "247",
      categories: [
        "Радиальные сальники (ГОСТ 8752-79, DIN 3760)",
        "Манжеты гидроцилиндров (ГОСТ 14896-84)",
        "V-образные манжеты (ГОСТ 22704-77)",
        "Уплотнения поршневые и штоковые",
        "O-кольца (ГОСТ 9833-73, ISO 3601)",
        "Грязесъемники и направляющие кольца",
      ],
      specifications: [
        "Диаметры: от 6мм до 2000мм",
        "Материалы: NBR, FKM, PTFE, PU, EPDM",
        "Температурный диапазон: -60°C до +300°C",
        "Давление: до 700 бар",
      ],
      applications: [
        "Гидравлические системы",
        "Пневматические системы",
        "Автомобильная промышленность",
        "Сельскохозяйственная техника",
        "Строительная техника",
        "Промышленное оборудование",
      ],
    },
  },
  {
    id: "filters",
    name: "Фильтрующие элементы",
    icon: Database,
    active: false,
    description: "Масляные, воздушные, топливные и гидравлические фильтры",
    detailedInfo: {
      totalParts: "Скоро",
      manufacturers: "150+",
      categories: [
        "Масляные фильтры двигателей",
        "Воздушные фильтры и элементы",
        "Топливные фильтры и сепараторы",
        "Гидравлические фильтры",
        "Салонные фильтры",
        "Сепараторы масла и воздуха",
      ],
      specifications: [
        "Степень фильтрации: от 1 до 200 микрон",
        "Рабочее давление: до 350 бар",
        "Температурный режим: -40°C до +150°C",
        "Типы соединений: резьбовые, фланцевые, байонетные",
      ],
      applications: [
        "Двигатели внутреннего сгорания",
        "Гидравлические системы",
        "Компрессорное оборудование",
        "Системы вентиляции",
      ],
    },
  },
]

const examplesData = [
  {
    title: "Сальники радиальные",
    original: "Corteco 12345-ABC",
    originalSpecs: {
      dimensions: "25×47×7mm",
      material: "NBR 70 Shore A",
      temperature: "-40°C до +120°C",
      speed: "до 15 м/с",
      standard: "DIN 3760",
    },
    compatible: [
      {
        part: "SKF 789-XYZ",
        match: "98%",
        price: "-35%",
        availability: "В наличии",
        specs: "25×47×7mm, NBR 70 Shore A, -40°C/+120°C",
      },
      {
        part: "Febi 456-DEF",
        match: "96%",
        price: "-28%",
        availability: "2-3 дня",
        specs: "25×47×7mm, NBR 72 Shore A, -35°C/+125°C",
      },
      {
        part: "NOK 321-GHI",
        match: "99%",
        price: "-42%",
        availability: "Под заказ",
        specs: "25×47×7mm, NBR 70 Shore A, -40°C/+120°C",
      },
    ],
    savings: "до 42%",
    description:
      "Радиальные сальники для валов с идентичными размерами и материалами. Все аналоги соответствуют стандарту DIN 3760 и имеют подтвержденную взаимозаменяемость.",
    technicalNote:
      "Различия в твердости материала (±2 Shore A) не влияют на эксплуатационные характеристики при стандартных условиях работы.",
  },
  {
    title: "Редукторы планетарные",
    original: "John Deere RE12345",
    originalSpecs: {
      ratio: "1:4.5",
      torque: "850 Нм",
      input: "1800 об/мин",
      efficiency: "96%",
      mounting: "Фланец SAE B",
    },
    compatible: [
      {
        part: "Komatsu 708-1W-00151",
        match: "95%",
        price: "-45%",
        availability: "В наличии",
        specs: "1:4.5, 850 Нм, 1800 об/мин, 95% КПД",
      },
      {
        part: "Caterpillar 123-4567",
        match: "93%",
        price: "-38%",
        availability: "1-2 дня",
        specs: "1:4.6, 820 Нм, 1800 об/мин, 94% КПД",
      },
    ],
    savings: "до 45%",
    description:
      "Планетарные редукторы для мобильной техники с совместимыми характеристиками и креплениями. Физически один агрегат, производимый под разными брендами.",
    technicalNote:
      "Незначительные отличия в передаточном числе (±0.1) и крутящем моменте (±30 Нм) находятся в пределах допустимых отклонений.",
  },
]

const pricingPlansData = [
  {
    name: "Базовый",
    price: "4,990",
    period: "мес",
    description: "Для небольших мастерских и сервисных центров",
    features: [
      "До 2,000 запросов в месяц",
      "Доступ к разделу «Сальники»",
      "Базовые технические характеристики",
      "Экспорт результатов в Excel/PDF",
      "Email поддержка (48 часов)",
      "Мобильное приложение",
    ],
    limitations: ["Ограниченный доступ к техническим чертежам", "Без API интеграции", "Стандартные отчеты"],
    popular: false,
  },
  {
    name: "Профессиональный",
    price: "12,990",
    period: "мес",
    description: "Для средних предприятий и дилерских центров",
    features: [
      "До 15,000 запросов в месяц",
      "Все активные разделы каталога",
      "Полные технические характеристики",
      "3D модели и чертежи деталей",
      "REST API для интеграции",
      "Приоритетная поддержка (24 часа)",
      "Расширенная аналитика и отчеты",
      "Персональный кабинет с историей",
      "Уведомления о новых аналогах",
    ],
    limitations: ["Ограничения по количеству API вызовов", "Стандартная интеграция"],
    popular: true,
  },
  {
    name: "Корпоративный",
    price: "29,990",
    period: "мес",
    description: "Для крупных производственных предприятий",
    features: [
      "Безлимитные запросы и API вызовы",
      "Все разделы + приоритетный доступ к новым",
      "Полная техническая документация",
      "CAD файлы и 3D модели",
      "Кастомная API интеграция",
      "Персональный технический менеджер",
      "SLA 99.9% доступности",
      "Белый лейбл решения",
      "Интеграция с ERP системами",
      "Обучение персонала",
      "Техническая поддержка 24/7",
    ],
    limitations: [],
    popular: false,
  },
]

export default function Page() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <div className="min-h-screen bg-black text-white overflow-hidden">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center">
        <div className="absolute inset-0 bg-black bg-grid-white/[0.02]" />
        <div className="absolute pointer-events-none inset-0 flex items-center justify-center bg-black [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)]" />

        <motion.div className="relative container mx-auto px-4 py-12 text-center z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: [0.25, 0.46, 0.45, 0.94] }}
          >
            <motion.div className="inline-flex items-center gap-3 bg-zinc-800 backdrop-blur-xl border border-zinc-700 text-gray-300 px-6 py-3 rounded-full text-base font-medium mb-4">
              <Database className="w-5 h-5 text-blue-400" />
              <span className="text-blue-400">Промышленная система каталогизации запчастей</span>
              <Sparkles className="w-4 h-4 text-blue-400" />
            </motion.div>

            <h1 className="text-6xl lg:text-7xl font-bold mb-4 leading-tight">
              Профессиональная база данных <span className="text-blue-400">взаимозаменяемых</span> запчастей
            </h1>

            <p className="text-2xl text-gray-100 mb-6 leading-relaxed max-w-4xl mx-auto">
              Система связывает физически идентичные или частично совместимые запчасти, узлы и агрегаты от различных
              производителей. Создана для инженеров, снабженцев и технических специалистов промышленных предприятий.
            </p>

            <div className="grid md:grid-cols-3 gap-6 mb-6 max-w-4xl mx-auto">
              {[
                { value: "500K+", label: "Запчастей в базе", color: "bg-zinc-700" },
                { value: "2,847", label: "Производителей", color: "bg-zinc-700" },
                { value: "до 50%", label: "Экономия на закупках", color: "bg-zinc-700" },
              ].map((stat, index) => (
                <motion.div key={index}>
                  <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-2xl p-6 text-center">
                    <div className={`text-4xl font-bold text-blue-400 mb-2`}>{stat.value}</div>
                    <div className="text-gray-100">{stat.label}</div>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg border-0">
                Получить доступ
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-zinc-700 text-gray-300 hover:bg-zinc-700 px-8 py-4 text-lg bg-zinc-800 backdrop-blur-xl"
              >
                Техническая документация
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>

      {/* Rest of the sections... */}

      {/* AISearchDemo Section */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
        <GridPattern className="opacity-20" />
        <div className="relative container mx-auto px-4">
          <AISearchDemo />
        </div>
      </AnimatedSection>

      {/* TechnicalSchema Section */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
        <GridPattern className="opacity-20" />
        <div className="relative container mx-auto px-4">
          <TechnicalSchema />
        </div>
      </AnimatedSection>

      {/* Manufacturers Network Section */}
      <AnimatedSection className="py-20 relative mt-16">
        <div className="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
        <GridPattern className="opacity-20" />
        <div className="relative container mx-auto px-4">
          <div className="text-center mb-8">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 bg-zinc-800 border border-zinc-700 text-blue-400 px-4 py-2 rounded-full text-base font-medium mb-3"
            >
              <Globe className="w-4 h-4" />
              Сеть производителей
            </motion.div>
            <h2 className="text-5xl lg:text-5xl font-bold mb-3">
              <span className="text-gray-300">Единая экосистема</span>{" "}
              <span className="text-blue-400">производителей</span>
            </h2>
            <p className="text-2xl text-gray-100 max-w-3xl mx-auto">
              Наш каталог объединяет ведущих мировых производителей в единую сеть взаимозаменяемых запчастей
            </p>
          </div>

          <ManufacturersNetwork />
        </div>
      </AnimatedSection>

      {/* Examples Section */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
        <div className="relative container mx-auto px-4">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 bg-zinc-800 border border-zinc-700 text-green-400 px-4 py-2 rounded-full text-base font-medium mb-3"
            >
              <CheckCircle className="w-4 h-4" />
              Реальные кейсы
            </motion.div>
            <h2 className="text-5xl lg:text-5xl font-bold mb-3">
              <span className="text-gray-300">Примеры взаимозаменяемости</span>{" "}
              <span className="text-green-400">деталей</span>
            </h2>
            <p className="text-2xl text-gray-100">
              Реальные кейсы поиска аналогов с техническими характеристиками и экономическим эффектом
            </p>
          </div>

          <div className="space-y-16">
            {examplesData.map((example, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 60 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-2xl overflow-hidden">
                  <div className="bg-zinc-800 p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 bg-zinc-700 rounded-xl flex items-center justify-center`}>
                          <Settings className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-3xl font-bold text-white">{example.title}</h3>
                          <p className="text-gray-100">{example.description}</p>
                        </div>
                      </div>
                      <Badge className="bg-green-500 text-white border-0 px-4 py-2 text-lg">
                        Экономия {example.savings}
                      </Badge>
                    </div>
                  </div>

                  <div className="grid lg:grid-cols-2 gap-0">
                    {/* Original Part */}
                    <div className="p-6 bg-zinc-900 backdrop-blur-xl border-r border-zinc-700">
                      <h4 className="font-semibold text-white mb-4 flex items-center gap-3">
                        <div className="w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center">
                          <Target className="w-4 h-4 text-white" />
                        </div>
                        Оригинальная деталь
                      </h4>

                      <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-6 mb-6">
                        <div className="font-mono text-2xl font-bold text-white mb-4">{example.original}</div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          {Object.entries(example.originalSpecs).map(([key, value]) => (
                            <div key={key} className="bg-zinc-900 rounded-lg p-3">
                              <div className="text-gray-300 capitalize text-sm mb-1">{key}:</div>
                              <div className="font-medium text-white">{value}</div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {example.technicalNote && (
                        <div className="bg-zinc-800 border border-zinc-700 rounded-xl p-4 backdrop-blur-xl">
                          <div className="flex items-start gap-3">
                            <Info className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                            <div className="text-sm">
                              <div className="font-medium text-blue-400 mb-2">Техническое примечание:</div>
                              <div className="text-gray-100">{example.technicalNote}</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Compatible Parts */}
                    <div className="p-6 bg-zinc-900 backdrop-blur-xl">
                      <h4 className="font-semibold text-white mb-4 flex items-center gap-3">
                        <div className="w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center">
                          <CheckCircle className="w-4 h-4 text-white" />
                        </div>
                        Совместимые аналоги
                      </h4>

                      <div className="space-y-4">
                        {example.compatible.map((part, i) => (
                          <motion.div
                            key={i}
                            initial={{ opacity: 0, x: 20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: i * 0.1 }}
                            viewport={{ once: true }}
                          >
                            <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4  transition-all duration-300">
                              <div className="flex items-center justify-between mb-3">
                                <div className="font-mono font-bold text-white">{part.part}</div>
                                <div className="flex items-center gap-2">
                                  <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
                                    {part.match} совместимость
                                  </Badge>
                                  <Badge className="bg-blue-500 text-white border-0">{part.price}</Badge>
                                </div>
                              </div>

                              <div className="text-sm text-gray-100 mb-3">{part.specs}</div>

                              <div className="flex items-center justify-between text-sm">
                                <span className="text-gray-300">Наличие:</span>
                                <span
                                  className={`font-medium ${
                                    part.availability === "В наличии"
                                      ? "text-green-400"
                                      : part.availability.includes("дня")
                                        ? "text-yellow-400"
                                        : "text-gray-400"
                                  }`}
                                >
                                  {part.availability}
                                </span>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* Catalog Sections */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
        <GridPattern className="opacity-10" />
        <div className="relative container mx-auto px-4">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 bg-zinc-800 border border-zinc-700 text-blue-400 px-4 py-2 rounded-full text-base font-medium mb-3"
            >
              <Database className="w-4 h-4" />
              Промышленный каталог
            </motion.div>
            <h2 className="text-5xl lg:text-5xl font-bold mb-3">
              <span className="text-gray-300">Разделы промышленного</span>{" "}
              <span className="text-blue-400">каталога</span>
            </h2>
            <p className="text-2xl text-gray-100">
              Комплексная система каталогизации запчастей по техническим категориям
            </p>
          </div>

          <div className="space-y-12">
            {catalogSectionsData.map((section, index) => {
              const Icon = section.icon
              return (
                <motion.div
                  key={section.id}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -60 : 60 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div
                    className={`bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-2xl ${section.active ? "" : "opacity-75"}`}
                  >
                    <div className="grid lg:grid-cols-3 gap-0">
                      {/* Header Section */}
                      <div className="p-6 bg-zinc-900">
                        <div className="flex items-center gap-4 mb-3">
                          <div
                            className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
                              section.active ? "bg-blue-500" : "bg-zinc-700"
                            }`}
                          >
                            <Icon className="w-8 h-8 text-white" />
                          </div>
                          <div>
                            <h3 className="text-3xl font-bold text-white">{section.name}</h3>
                            {section.active && (
                              <div className="flex items-center gap-2 mt-2">
                                <div className="w-2 h-2 bg-green-400 rounded-full" />
                                <span className="text-green-400 text-sm font-medium">Активен</span>
                              </div>
                            )}
                          </div>
                        </div>

                        <p className="text-lg text-gray-100 mb-4">{section.description}</p>

                        <div className="grid grid-cols-2 gap-6">
                          <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4">
                            <div
                              className={`text-3xl font-bold mb-1 ${
                                section.active ? "text-blue-400" : "text-gray-400"
                              }`}
                            >
                              {section.detailedInfo.totalParts}
                            </div>
                            <div className="text-sm text-gray-100">Позиций в каталоге</div>
                          </div>
                          <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4">
                            <div
                              className={`text-3xl font-bold mb-1 ${
                                section.active ? "text-green-400" : "text-gray-400"
                              }`}
                            >
                              {section.detailedInfo.manufacturers}
                            </div>
                            <div className="text-sm text-gray-100">Производителей</div>
                          </div>
                        </div>

                        {!section.active && (
                          <div className="mt-4 p-4 bg-zinc-800 border border-zinc-700 rounded-xl backdrop-blur-xl">
                            <div className="flex items-center gap-2 text-yellow-400 text-sm font-medium">
                              <Clock className="w-4 h-4" />
                              Раздел появится в ближайшее время
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Categories Section */}
                      <div className="p-6 bg-zinc-900 border-l border-zinc-700">
                        <h4 className="font-semibold text-white mb-4 flex items-center gap-3">
                          <div className="w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center">
                            <Cpu className="w-4 h-4 text-white" />
                          </div>
                          Категории деталей
                        </h4>

                        <div className="space-y-3 mb-4">
                          {section.detailedInfo.categories.map((category, i) => (
                            <motion.div
                              key={i}
                              initial={{ opacity: 0, x: -20 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              transition={{ delay: i * 0.1 }}
                              viewport={{ once: true }}
                              className="flex items-center gap-3 text-sm group"
                            >
                              <ChevronRight className="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors" />
                              <span
                                className={`${section.active ? "text-gray-300" : "text-gray-500"} group-hover:text-white transition-colors`}
                              >
                                {category}
                              </span>
                            </motion.div>
                          ))}
                        </div>

                        <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-xl p-4">
                          <h5 className="font-medium text-white mb-3 flex items-center gap-2">
                            <Settings className="w-4 h-4 text-blue-400" />
                            Технические характеристики:
                          </h5>
                          <div className="space-y-2">
                            {section.detailedInfo.specifications.map((spec, i) => (
                              <div key={i} className="text-sm text-gray-100 flex items-center gap-2">
                                <div className="w-1 h-1 bg-blue-400 rounded-full" />
                                {spec}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Applications Section */}
                      <div className="p-6 bg-zinc-900 border-l border-zinc-700">
                        <h4 className="font-semibold text-white mb-4 flex items-center gap-3">
                          <div className="w-8 h-8 bg-zinc-700 rounded-lg flex items-center justify-center">
                            <Factory className="w-4 h-4 text-white" />
                          </div>
                          Области применения
                        </h4>

                        <div className="space-y-4 mb-4">
                          {section.detailedInfo.applications.map((app, i) => (
                            <motion.div
                              key={i}
                              initial={{ opacity: 0, y: 20 }}
                              whileInView={{ opacity: 1, y: 0 }}
                              transition={{ delay: i * 0.1 }}
                              viewport={{ once: true }}
                            >
                              <div className="flex items-center gap-4 p-4 bg-zinc-800 backdrop-blur-xl rounded-xl border border-zinc-700  transition-all duration-300">
                                <Building2 className="w-5 h-5 text-gray-400" />
                                <span className={`text-sm ${section.active ? "text-gray-300" : "text-gray-500"}`}>
                                  {app}
                                </span>
                              </div>
                            </motion.div>
                          ))}
                        </div>

                        {section.active && (
                          <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white border-0">
                            Перейти к каталогу
                            <ArrowRight className="ml-2 w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>
      </AnimatedSection>

      {/* Pricing Section */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-black via-zinc-950 to-black" />
        <div className="relative container mx-auto px-4">
          <div className="text-center mb-12">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 bg-zinc-800 border border-zinc-700 text-green-400 px-4 py-2 rounded-full text-base font-medium mb-3"
            >
              <Star className="w-4 h-4" />
              Тарифные планы
            </motion.div>
            <h2 className="text-5xl lg:text-5xl font-bold mb-3">
              <span className="text-gray-300">Тарифные планы для</span>{" "}
              <span className="text-green-400">профессионалов</span>
            </h2>
            <p className="text-2xl text-gray-100">Выберите подходящий уровень доступа к промышленной базе данных</p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {pricingPlansData.map((plan, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 60 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative h-full">
                  {plan.popular && (
                    <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="absolute -top-6 left-1/2 transform -translate-x-1/2 z-10"
                    >
                      <div className="bg-blue-600 text-white px-6 py-3 rounded-full text-sm font-medium flex items-center gap-2">
                        <Star className="w-4 h-4" />
                        Рекомендуемый
                        <Sparkles className="w-4 h-4 text-blue-400" />
                      </div>
                    </motion.div>
                  )}

                  <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-2xl p-6 h-full flex flex-col">
                    <div className="text-center mb-8">
                      <h3 className="text-3xl font-bold text-white mb-3">{plan.name}</h3>
                      <p className="text-gray-100 mb-6">{plan.description}</p>

                      <div className="text-center">
                        <div className="text-5xl font-bold mb-2 text-blue-400">{plan.price}₽</div>
                        <div className="text-gray-500">за {plan.period}</div>
                      </div>
                    </div>

                    <div className="flex-1 space-y-6">
                      <div>
                        <h4 className="font-semibold text-white mb-4 flex items-center gap-2">
                          <CheckCircle className="w-5 h-5 text-green-400" />
                          Включено в план
                        </h4>
                        <ul className="space-y-3">
                          {plan.features.map((feature, i) => (
                            <motion.li
                              key={i}
                              initial={{ opacity: 0, x: -20 }}
                              whileInView={{ opacity: 1, x: 0 }}
                              transition={{ delay: i * 0.05 }}
                              viewport={{ once: true }}
                              className="flex items-start gap-3 text-sm"
                            >
                              <CheckCircle2 className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-100">{feature}</span>
                            </motion.li>
                          ))}
                        </ul>
                      </div>

                      {plan.limitations.length > 0 && (
                        <div>
                          <h4 className="font-semibold text-white mb-4 flex items-center gap-2">
                            <AlertTriangle className="w-5 h-5 text-yellow-400" />
                            Ограничения
                          </h4>
                          <ul className="space-y-3">
                            {plan.limitations.map((limitation, i) => (
                              <li key={i} className="flex items-start gap-3 text-sm">
                                <div className="w-4 h-4 border border-gray-500 rounded mt-0.5 flex-shrink-0" />
                                <span className="text-gray-300">{limitation}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>

                    <Button
                      className={`w-full text-lg py-4 border-0 ${
                        plan.popular
                          ? "bg-blue-600 hover:bg-blue-700 text-white"
                          : "bg-blue-600 hover:bg-blue-700 text-white"
                      }`}
                    >
                      Выбрать план
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </AnimatedSection>

      {/* CTA Section */}
      <AnimatedSection className="py-16 relative">
        <div className="absolute inset-0 bg-gradient-to-br from-zinc-900 via-black to-zinc-950" />
        <div className="relative container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 bg-zinc-800 border border-zinc-700 text-blue-400 px-4 py-2 rounded-full text-base font-medium mb-4"
            >
              <Globe className="w-4 h-4" />
              Присоединяйтесь к лидерам индустрии
            </motion.div>

            <h2 className="text-5xl lg:text-6xl font-bold mb-4">
              <span className="text-gray-300">Готовы оптимизировать</span>{" "}
              <span className="text-blue-400">закупки запчастей?</span>
            </h2>

            <p className="text-2xl text-gray-100 mb-6 max-w-4xl mx-auto leading-relaxed">
              Присоединяйтесь к промышленным предприятиям, которые уже экономят миллионы рублей благодаря нашей системе
              поиска взаимозаменяемых деталей
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-8">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-10 py-5 text-lg border-0">
                Запросить демонстрацию
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-zinc-700 text-gray-300 hover:bg-zinc-700 px-10 py-5 text-lg bg-zinc-800 backdrop-blur-xl"
              >
                Техническая консультация
              </Button>
            </div>

            <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {[
                {
                  icon: Shield,
                  title: "Гарантия качества",
                  description: "Все связи проверены экспертами",
                  color: "bg-zinc-700",
                },
                {
                  icon: Zap,
                  title: "Быстрая интеграция",
                  description: "API готов к работе за 24 часа",
                  color: "bg-zinc-700",
                },
                {
                  icon: Users,
                  title: "Экспертная поддержка",
                  description: "Команда инженеров 24/7",
                  color: "bg-zinc-700",
                },
              ].map((feature, index) => (
                <motion.div key={index}>
                  <div className="bg-zinc-800 backdrop-blur-xl border border-zinc-700 rounded-2xl p-6 text-center  transition-all duration-300">
                    <div className={`w-16 h-16 bg-zinc-700 rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                      <feature.icon className="w-8 h-8 text-white" />
                    </div>
                    <div className="font-semibold text-white text-lg mb-2">{feature.title}</div>
                    <div className="text-sm text-gray-100">{feature.description}</div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </AnimatedSection>
    </div>
  )
}
