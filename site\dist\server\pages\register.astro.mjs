import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { B as Button, a as authClient, $ as $$MainLayout } from '../chunks/MainLayout_CUaM-0Uq.mjs';
import { jsxs, jsx } from 'react/jsx-runtime';
import { useState } from 'react';
import { I as Input } from '../chunks/input_D169YdkW.mjs';
export { renderers } from '../renderers.mjs';

function RegisterForm() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const onSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);
    try {
      await authClient.signUp.email(
        { email, password, name },
        {
          onError: (ctx) => setError(ctx.error.message ?? "Ошибка регистрации"),
          onSuccess: () => {
            window.location.href = "/account";
          }
        }
      );
    } finally {
      setLoading(false);
    }
  };
  return /* @__PURE__ */ jsxs("form", { className: "space-y-4", onSubmit, children: [
    /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsx("label", { className: "text-sm", children: "Имя" }),
      /* @__PURE__ */ jsx(Input, { value: name, onChange: (e) => setName(e.target.value) })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsx("label", { className: "text-sm", children: "Email" }),
      /* @__PURE__ */ jsx(Input, { type: "email", value: email, onChange: (e) => setEmail(e.target.value), required: true })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsx("label", { className: "text-sm", children: "Пароль" }),
      /* @__PURE__ */ jsx(Input, { type: "password", value: password, onChange: (e) => setPassword(e.target.value), required: true })
    ] }),
    error && /* @__PURE__ */ jsx("div", { className: "text-sm text-red-500", children: error }),
    /* @__PURE__ */ jsx(Button, { type: "submit", disabled: loading, children: loading ? "Регистрируем..." : "Зарегистрироваться" })
  ] });
}

const $$Astro = createAstro();
const $$Register = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Register;
  if (Astro2.locals.user) {
    return Astro2.redirect("/account");
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u044F" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<section class="container py-10"> <div class="mx-auto max-w-md"> <h1 class="mb-6 text-2xl font-bold">Регистрация</h1> ${renderComponent($$result2, "RegisterForm", RegisterForm, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/auth/RegisterForm", "client:component-export": "default" })} <p class="mt-4 text-sm text-muted-foreground">
Уже есть аккаунт? <a href="/login" class="underline">Войдите</a> </p> </div> </section> ` })}`;
}, "D:/Dev/parttec/site/src/pages/register.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/register.astro";
const $$url = "/register";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Register,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
