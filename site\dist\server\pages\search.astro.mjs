import { e as createComponent, f as createAstro, k as renderComponent, r as renderTemplate, m as maybeRenderHead, h as addAttribute } from '../chunks/astro/server_D7mwM5eH.mjs';
import 'kleur/colors';
import { $ as $$MainLayout } from '../chunks/MainLayout_CUaM-0Uq.mjs';
import { t as trpcClient } from '../chunks/badge_QUEVfRm2.mjs';
import { T as TrpcProvider } from '../chunks/TrpcProvider_Btxr0xOO.mjs';
import { S as SearchForm } from '../chunks/SearchForm_CKb7LlK9.mjs';
import { P as PartCard } from '../chunks/PartCard_Bm_8W9KW.mjs';
import { C as CategoryCard } from '../chunks/CategoryCard_BIi6TMrC.mjs';
import { B as BrandCard } from '../chunks/BrandCard_PBJFKcEA.mjs';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro();
const $$Search = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Search;
  const searchParams = Astro2.url.searchParams;
  const searchQuery = searchParams.get("q") || "";
  let searchResults = {
    parts: [],
    categories: [],
    brands: []
  };
  if (searchQuery.trim()) {
    try {
      const partsResponse = await trpcClient.crud.part.findMany.query({
        where: {
          OR: [
            { name: { contains: searchQuery, mode: "insensitive" } },
            { partCategory: { name: { contains: searchQuery, mode: "insensitive" } } },
            { attributes: { some: { value: { contains: searchQuery, mode: "insensitive" } } } }
          ]
        },
        include: {
          partCategory: true,
          image: true,
          attributes: {
            include: { template: true },
            take: 3
          }
        },
        take: 20,
        orderBy: { updatedAt: "desc" }
      });
      searchResults.parts = partsResponse || [];
      const categoriesResponse = await trpcClient.crud.partCategory.findMany.query({
        where: {
          OR: [
            { name: { contains: searchQuery, mode: "insensitive" } },
            { description: { contains: searchQuery, mode: "insensitive" } }
          ]
        },
        include: {
          image: true,
          _count: { select: { parts: true } }
        },
        take: 10,
        orderBy: { name: "asc" }
      });
      searchResults.categories = categoriesResponse || [];
      const brandsResponse = await trpcClient.crud.brand.findMany.query({
        where: {
          OR: [
            { name: { contains: searchQuery, mode: "insensitive" } },
            { country: { contains: searchQuery, mode: "insensitive" } }
          ]
        },
        include: {
          _count: {
            select: {
              catalogItems: true,
              equipmentModel: true
            }
          }
        },
        take: 10,
        orderBy: { name: "asc" }
      });
      searchResults.brands = brandsResponse || [];
    } catch (error) {
      console.error("Error performing search:", error);
    }
  }
  const totalResults = searchResults.parts.length + searchResults.categories.length + searchResults.brands.length;
  const handleSearch = (query) => {
    if (query.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
  };
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "\u041F\u043E\u0438\u0441\u043A", "description": "\u0420\u0430\u0441\u0448\u0438\u0440\u0435\u043D\u043D\u044B\u0439 \u043F\u043E\u0438\u0441\u043A \u043F\u043E \u043A\u0430\u0442\u0430\u043B\u043E\u0433\u0443 \u0437\u0430\u043F\u0447\u0430\u0441\u0442\u0435\u0439" }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "TrpcProvider", TrpcProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/providers/TrpcProvider", "client:component-export": "TrpcProvider" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <!-- Заголовок и форма поиска --> <div class="mb-8"> <h1 class="text-3xl font-bold tracking-tight mb-4">Поиск по каталогу</h1> <div class="max-w-md"> ${renderComponent($$result3, "SearchForm", SearchForm, { "client:load": true, "onSearch": handleSearch, "defaultValue": searchQuery, "placeholder": "\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u0430\u043F\u0440\u043E\u0441 \u0434\u043B\u044F \u043F\u043E\u0438\u0441\u043A\u0430...", "client:component-hydration": "load", "client:component-path": "@/components/catalog/SearchForm", "client:component-export": "SearchForm" })} </div> </div> ${searchQuery.trim() ? renderTemplate`<div> <!-- Результаты поиска --> <div class="mb-6"> <h2 class="text-xl font-semibold mb-2">
Результаты поиска для "${searchQuery}"
</h2> <p class="text-muted-foreground">
Найдено ${totalResults} результатов
</p> </div> ${totalResults > 0 ? renderTemplate`<div class="space-y-8"> <!-- Запчасти --> ${searchResults.parts.length > 0 && renderTemplate`<div> <h3 class="text-lg font-semibold mb-4">
Запчасти (${searchResults.parts.length})
</h3> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"> ${searchResults.parts.map((part) => renderTemplate`${renderComponent($$result3, "PartCard", PartCard, { "part": part, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/PartCard", "client:component-export": "PartCard" })}`)} </div> ${searchResults.parts.length === 20 && renderTemplate`<div class="mt-4"> <a${addAttribute(`/catalog?search=${encodeURIComponent(searchQuery)}`, "href")} class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
Показать все запчасти
</a> </div>`} </div>`} <!-- Категории --> ${searchResults.categories.length > 0 && renderTemplate`<div> <h3 class="text-lg font-semibold mb-4">
Категории (${searchResults.categories.length})
</h3> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"> ${searchResults.categories.map((category) => renderTemplate`${renderComponent($$result3, "CategoryCard", CategoryCard, { "category": category, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/CategoryCard", "client:component-export": "CategoryCard" })}`)} </div> </div>`} <!-- Бренды --> ${searchResults.brands.length > 0 && renderTemplate`<div> <h3 class="text-lg font-semibold mb-4">
Бренды (${searchResults.brands.length})
</h3> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"> ${searchResults.brands.map((brand) => renderTemplate`${renderComponent($$result3, "BrandCard", BrandCard, { "brand": brand, "client:load": true, "client:component-hydration": "load", "client:component-path": "@/components/catalog/BrandCard", "client:component-export": "BrandCard" })}`)} </div> </div>`} </div>` : renderTemplate`<div class="text-center py-12"> <h3 class="text-lg font-semibold mb-2">Ничего не найдено</h3> <p class="text-muted-foreground mb-4">
Попробуйте изменить поисковый запрос или воспользуйтесь каталогом
</p> <div class="flex justify-center gap-4"> <a href="/catalog" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground shadow hover:bg-primary/90 h-9 px-4 py-2">
Открыть каталог
</a> <a href="/categories" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
Просмотреть категории
</a> </div> </div>`} </div>` : renderTemplate`<div class="text-center py-12"> <h3 class="text-lg font-semibold mb-2">Введите поисковый запрос</h3> <p class="text-muted-foreground mb-6">
Используйте форму выше для поиска запчастей, категорий или брендов
</p> <!-- Популярные поисковые запросы --> <div class="max-w-md mx-auto"> <h4 class="text-sm font-medium mb-3">Популярные запросы:</h4> <div class="flex flex-wrap gap-2 justify-center"> <a href="/search?q=фильтр" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
фильтр
</a> <a href="/search?q=сальник" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
сальник
</a> <a href="/search?q=подшипник" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
подшипник
</a> <a href="/search?q=прокладка" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
прокладка
</a> <a href="/search?q=ремень" class="px-3 py-1 text-xs bg-muted rounded-full hover:bg-muted/80 transition-colors">
ремень
</a> </div> </div> </div>`} </div> ` })} ` })}`;
}, "D:/Dev/parttec/site/src/pages/search.astro", void 0);

const $$file = "D:/Dev/parttec/site/src/pages/search.astro";
const $$url = "/search";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Search,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
